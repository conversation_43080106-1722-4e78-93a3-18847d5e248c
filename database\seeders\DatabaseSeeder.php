<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\School\System\School;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Database\Seeders\assessment\SchedulesSeeder;
use Database\Seeders\assessment\career\TaskAssignmentsSeeder;
use Database\Seeders\assessment\career\AnswersSeeder;
use Database\Seeders\assessment\competency\AssignmentsSeeder as CompetencyAssignmentsSeeder;
use Database\Seeders\assessment\competency\AnswersSeeder as CompetencyAnswersSeeder;
use Database\Seeders\assessment\capability\AssignmentsSeeder as CapabilityAssignmentsSeeder;
use Database\Seeders\assessment\capability\AnswersSeeder as CapabilityAnswersSeeder;
use Database\Seeders\assessment\psychology\TaskAssignmentsSeeder as PsychologyAssignmentsSeeder;
use Database\Seeders\assessment\psychology\AnswersSeeder as PsychologyAnswersSeeder;
use Database\Seeders\assessment\subject\TaskAssignmentsSeeder as SubjectAssignmentsSeeder;
use Database\Seeders\assessment\subject\AnswersSeeder as SubjectAnswersSeeder;
class DatabaseSeeder extends Seeder
{
    protected string $connect = 'mysql_prod';
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // 将内存限制设置为 512MB
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', 8000);

//        // 1.1查询老库所有学校ID(跑SchoolSeeder数据，使用老库学校数据)
        $schoolIds = \DB::connection($this->connect)->table('school')
            ->where('step', '>=', 0)
            ->where('date_due', '>=', date('Y-m-d'))
            ->orderBy('id', 'asc')
            ->pluck('id')->toArray();

//        // 1.2查询新库所有学校ID(跑其他seeder数据，使用新库学校数据)
//        $schoolIds = School::where('status', 1)
////            ->where('id', '<=',200)
//            ->orderBy('id', 'asc')
////            ->skip(0)->take(200)
//            ->pluck('id')->toArray();

        //$schoolIds = [761,763,886,889,965,966,973,979,987];//学科只有这几个学校
        //$schoolIds = [508,761,763,842,886,889,913,965,966];//创新人才核心素养测评只有这几个学校

        // 单个学校
//        $schoolIds = [761];


        // 查看当前数据库
        $dbName = DB::connection()->getDatabaseName();
        if ($dbName === 'shengya_yishengya') {
            $seeders = $this->getSeeders();
            foreach ($seeders as $seeder) {
                $start_time = date('Y-m-d H:i:s');
                echo "数据填充类 {$seeder} 开始执行：" . $start_time . "\n";
                $num = 1;
                foreach ($schoolIds as $schoolId) {
                    Log::info("第{$num}个学校（ID：" . $schoolId . "） {$seeder} 数据迁移start：" . date('Y-m-d H:i:s'));
                    echo "第{$num}个学校（ID：" . $schoolId . "） {$seeder} 数据迁移start：" . date('Y-m-d H:i:s') . "\n";

                    $s = new $seeder($schoolId);
                    $s->run();

                    echo "第{$num}个学校（ID：" . $schoolId . "） {$seeder} 数据迁移end：" . date('Y-m-d H:i:s') . "\n";
                    Log::info("第{$num}个学校（ID：" . $schoolId . "） {$seeder} 数据迁移end：" . date('Y-m-d H:i:s'));
                    $num++;
                }
                $end_time = date('Y-m-d H:i:s');
                echo "数据填充类 {$seeder} 执行完毕：从 {$start_time} 至 {$end_time} ；共用时：" . ((strtotime($end_time) - strtotime($start_time))/60) . "分钟\n\n";
            }
        }
    }

    /**
     * 获取所有数据填充类
     *
     * @return array
     */
    protected function getSeeders(): array
    {
        return [
            // 基础数据
            // 跑SchoolSeeder数据，使用老库学校数据，其他数据使用新库数据
//             SchoolSeeder::class,
//            RoleSeeder_2::class, // RoleSeeder 的简化版
//            ClassSeeder::class, // ClassSeeder执行完毕后，手动执行：数据迁移：班级数据处理.sql
//            CourseSeeder::class,
//            StudentSeeder_2::class, // 跑StudentSeeder数据之前，数据预处理：在原库创建ysy_student_convert临时表，将转换后的学生和账号信息通过sql（转换多条Student数据为一条）导入到该表；
//            StudentClassRelationSeeder_2::class,
           TeacherSeeder_2::class,
//            TeacherViewClassSeeder::class,

            // 学生老师用户账号跑完后， 再手动跑：数据迁移：插入用户角色关系.sql

//
//            // 测评
//            SchedulesSeeder::class,//此seeder跑完后，需要将assessment_schedules表和assessment_tasks表复制到老系统中并加前缀ysy_
        //    TaskAssignmentsSeeder::class,
        //    AnswersSeeder::class,
//            CompetencyAssignmentsSeeder::class,
//            CompetencyAnswersSeeder::class,
        //    CapabilityAssignmentsSeeder::class,
        //    CapabilityAnswersSeeder::class,
        //    PsychologyAssignmentsSeeder::class,
        //    PsychologyAnswersSeeder::class,
        //    SubjectAssignmentsSeeder::class,
        //    SubjectAnswersSeeder::class,


        ];

    }
}
