<?php

namespace Database\Seeders;

use Illuminate\Database\QueryException;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 教师数据填充
 * User数据填充
 * 优化版本：减少N+1查询，使用事务，优化批量大小
 */
class TeacherSeeder_2 extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    protected int $teacherType = 3;
    protected int $batchSize = 200; // 进一步减少批量大小，提高处理速度
    protected int $maxMemoryUsage = 128 * 1024 * 1024; // 128MB 内存限制

    /**
     * 构造函数
     * 
     * @param int $schoolId 学校ID
     */
    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     * 执行教师数据填充
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        $start_time = microtime(true);

        echo "开始处理学校ID：{$school_id} 的教师数据迁移...\n";

        //  获取学校对应的机构ID
        $orgID = DB::table('organizations')
            ->where('model_id', $this->school_id)
            ->where('model_type', 'school')
            ->value('id');

        if (!$orgID) {
            echo "错误：未找到学校ID {$school_id} 对应的机构信息\n";
            return;
        }

        $user_total = 0;
        $teacher_total = 0;
        $teacher_error_total = 0;
        $num = 1;

        try {
            // 使用更高效的方式：一次性获取所有数据，然后在内存中处理
            $this->processDataInBatches($school_id, $orgID, $user_total, $teacher_total, $teacher_error_total, $num);

            // 完成迁移并输出统计信息
            $this->finishMigration($school_id, $start_time, $user_total, $teacher_total, $teacher_error_total);

        } catch (QueryException $e) {
            echo "迁移过程发生错误：" . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * 高效批量处理数据
     */
    private function processDataInBatches($school_id, $orgID, &$user_total, &$teacher_total, &$teacher_error_total, &$num): void
    {
        // 获取学校角色列表
        $roleList = DB::connection($this->connect)->table('role')
            ->where('school_id', $school_id)
            ->where('step', 0)
            ->whereIn('type', [2, 3])
            ->get();

        foreach ($roleList as $role) {
            $old_role_id = $role->id;
            $old_role_id_str = "0,{$role->id},0";
            $type = $role->type;

            echo "处理角色ID：{$old_role_id}，类型：{$type}\n";

            // 使用游标分页，避免 chunk 的性能问题
            $this->processRoleMembers($old_role_id, $old_role_id_str, $type, $orgID, $school_id, $user_total, $teacher_total, $teacher_error_total, $num);
        }
    }

    /**
     * 处理角色下的成员数据
     */
    private function processRoleMembers($old_role_id, $old_role_id_str, $type, $orgID, $school_id, &$user_total, &$teacher_total, &$teacher_error_total, &$num): void
    {
        $offset = 0;

        do {
            // 检查内存使用情况
            if (memory_get_usage(true) > $this->maxMemoryUsage) {
                gc_collect_cycles();
                echo "内存使用过高，执行垃圾回收\n";
            }

            // 使用 LIMIT/OFFSET 替代 chunk，性能更好
            $members = DB::connection($this->connect)->table('member')
                ->where('role_id', $old_role_id_str)
                ->where('step', 0)
                ->orderBy('id')
                ->offset($offset)
                ->limit($this->batchSize)
                ->get();

            if ($members->isEmpty()) {
                break;
            }

            $this->processMemberBatch($members, $old_role_id, $type, $orgID, $school_id, $user_total, $teacher_total, $teacher_error_total, $num);

            $offset += $this->batchSize;

        } while (true);
    }

    /**
     * 处理单批成员数据
     */
    private function processMemberBatch($members, $old_role_id, $type, $orgID, $school_id, &$user_total, &$teacher_total, &$teacher_error_total, &$num): void
    {
        // 预加载教师信息，避免N+1查询
        $memberIds = $members->pluck('id')->toArray();
        $teacherInfoMap = [];

        if ($type == $this->teacherType) {
            $teacherInfos = DB::connection($this->connect)->table('teacher')
                ->whereIn('member_id', $memberIds)
                ->where('step', 0)
                ->get()
                ->keyBy('member_id');
            $teacherInfoMap = $teacherInfos->toArray();
        }

        $user_data = [];
        $teacher_data = [];
        $teacher_error_data = [];

        foreach ($members as $member) {
            // 教师和教务用户数据
            $user_data[] = [
                'id' => $member->id,
                'organization_id' => $orgID,
                'username' => $member->username,
                'real_name' => $member->name,
                'phone' => $member->mobile,
                'gender' => $member->gender > 1 ? 2 : 1,
                'md5_password' => $member->password,
                'role_id' => $old_role_id,
                'creator' => '教师',
            ];
            $user_total++;

            // 如果是教师角色，准备教师数据
            if ($type == $this->teacherType) {
                $teacher_info = $teacherInfoMap[$member->id] ?? null;

                if ($teacher_info) {
                    $teacher_data[] = [
                        'id' => $member->id,
                        'teacher_id' => $teacher_info->id,
                        'user_id' => $member->id,
                        'school_id' => $school_id,
                        'school_campus_id' => $teacher_info->school_district,
                        'teacher_name' => $teacher_info->name,
                        'is_psychology_teacher' => $teacher_info->is_psych,
                    ];
                    $teacher_total++;
                } else {
                    $teacher_error_data[] = [$member->id];
                    $teacher_error_total++;
                }
            }
        }

        // 使用事务批量插入数据
        DB::beginTransaction();
        try {
            if (!empty($user_data)) {
                DB::table('users')->insertOrIgnore($user_data);
                echo "学校ID：{$school_id}，迁移第{$num}批用户数据(" . count($user_data) . "条) " . date('Y-m-d H:i:s') . "\n";
            }

            if (!empty($teacher_data)) {
                DB::table('teachers')->insertOrIgnore($teacher_data);
                echo "学校ID：{$school_id}，迁移第{$num}批教师数据(" . count($teacher_data) . "条) " . date('Y-m-d H:i:s') . "\n";
            }

            if (!empty($teacher_error_data)) {
                $teacher_error_data_str = implode(',', array_map(fn($item) => implode(',', $item), $teacher_error_data));
                echo "学校ID：{$school_id}，第{$num}批教师错误数据用户ID：{$teacher_error_data_str}\n";
            }

            DB::commit();
            $num++;
        } catch (QueryException $e) {
            DB::rollBack();
            echo "批量插入失败：" . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * 完成数据迁移并输出统计信息
     */
    private function finishMigration($school_id, $start_time, $user_total, $teacher_total, $teacher_error_total): void
    {
        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 2);

        echo "学校ID：{$school_id} 数据迁移完成！\n";
        echo "- 共迁移 {$user_total} 条用户数据\n";
        echo "- 成功迁移 {$teacher_total} 条教师数据\n";
        echo "- {$teacher_error_total} 条教师数据未找到\n";
        echo "- 执行时间：{$execution_time} 秒\n";
        echo "- 平均处理速度：" . round(($user_total + $teacher_total) / $execution_time, 2) . " 条/秒\n";
        echo "- 完成时间：" . date('Y-m-d H:i:s') . "\n";

        // 强制垃圾回收，释放内存
        gc_collect_cycles();
    }
}