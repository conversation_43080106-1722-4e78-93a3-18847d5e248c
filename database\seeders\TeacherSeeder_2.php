<?php

namespace Database\Seeders;

use Illuminate\Database\QueryException;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 教师数据填充
 * User数据填充
 */
class TeacherSeeder_2 extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    protected int $teacherType = 3;
    protected int $batchSize = 1000;

    /**
     * 构造函数
     * 
     * @param int $schoolId 学校ID
     */
    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     * 执行教师数据填充
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        //  获取学校对应的机构ID
        $orgID = DB::table('organizations')
            ->where('model_id', $this->school_id)
            ->where('model_type', 'school')
            ->value('id');

        $user_total = 0;
        $teacher_total = 0;
        $teacher_error_total = 0;
        $num = 1;

        try {
            // 获取学校角色列表
            $roleList = DB::connection($this->connect)->table('role')
                ->where('school_id', $school_id)
                ->where('step', 0)
                ->whereIn('type', [2, 3]) // 角色类型 2教务3教师
                ->get();

            // 处理每个角色下的账号
            foreach ($roleList as $role) {
                $old_role_id = $role->id;
                $old_role_id_str = '0,'.$role->id.',0';
                $type = $role->type;

                // 分批查询角色下的账号列表
                DB::connection($this->connect)->table('member')
                    ->where('role_id', $old_role_id_str)
                    ->where('step', 0)
                    ->orderBy('id')
                    ->chunk($this->batchSize, function ($members) use ($old_role_id, $orgID, $type, $school_id, &$teacher_error_total, &$user_total, &$teacher_total, &$num) {
                        $user_data = [];
                        $teacher_data = [];
                        $teacher_error_data = [];

                        foreach ($members as $member) {
                            // 教师和教务用户数据
                            $user_data[] = [
                                'id' => $member->id, // 原始ID
                                'organization_id' => $orgID, // 新增机构ID
                                'username' => $member->username,
                                'real_name' => $member->name,
                                'phone' => $member->mobile,
                                'gender' => $member->gender > 1 ? 2 : 1,
                                'md5_password' => $member->password,
                                'role_id' => $old_role_id, // 存原始角色id，在最后统一更新用户角色关系
//                                'openid' => $member->id, // 临时存储原来用户表id，用于批量导入后批量更新Teacher表user_id
                                'creator' => '教师',
                            ];
                            $user_total++;

                            // 如果是教师角色，准备教师数据
                            if ($type == $this->teacherType) {
                                // 查询账号对应的教师信息
                                $teacher_info = DB::connection($this->connect)->table('teacher')
                                    ->where('member_id', $member->id)
                                    ->where('step', 0)
                                    ->first();

                                if ($teacher_info) {
                                    // 教师数据
                                    $teacher_data[] = [
                                        'id' => $member->id,
                                        'teacher_id' => $teacher_info->id, // 冗余字段，便于下次更新带班等关系表信息
                                        'user_id' => $member->id,
                                        'school_id' => $school_id,
                                        'school_campus_id' => $teacher_info->school_district, // 之前导入的是校区时，校区主键ID没有变更
                                        'teacher_name' => $teacher_info->name,
                                        'is_psychology_teacher' => $teacher_info->is_psych,
                                        // 'member_id' => $member->id, // 临时存储，用于后续更新user_id
                                    ];
                                    $teacher_total++;
                                } else {
                                    // 教师错误数据
                                    $teacher_error_data[] = [
                                        $member->id, // 临时存储，用于后续更新user_id
                                    ];
                                    $teacher_error_total++;
                                }
                            }
                        }

                        // 批量插入用户数据
                        try {
                            if (!empty($user_data)) {
                                DB::table('users')->insertOrIgnore($user_data);
                                $user_data = []; // 清空数组
                                echo "学校ID：" . $school_id . "，迁移第" . $num . "批教师/教务用户数据 " . date('Y-m-d H:i:s') . "\n";
                            }

                            // 批量插入教师数据
                            if (!empty($teacher_data)) {
                                DB::table('teachers')->insertOrIgnore($teacher_data);
                                $teacher_data = []; // 清空数组
                                echo "学校ID：" . $school_id . "，迁移第" . $num . "批教师数据 " . date('Y-m-d H:i:s') . "\n";
                            }
                            // 批量插入教师错误数据
                            if (!empty($teacher_error_data)) {
                                // 将数组转换为字符串
                                $teacher_error_data_str = implode(',', array_map(function($item) {
                                    return implode(',', $item);
                                }, $teacher_error_data));
                                echo "学校ID：". $school_id. "，迁移第". $num. "批教师错误数据用户ID：{$teacher_error_data_str} " . "\n";
                            }

                            $num++;
                        } catch (QueryException $e) {
                            echo "批量插入失败：" . $e->getMessage() . "\n";
                        }
                    });
            }

            echo "学校ID：" . $school_id . "，共迁移{$user_total}条数据老师用户数据，成功迁移{$teacher_total}条老师数据，{$teacher_error_total}条老师数据未找到 " . date('Y-m-d H:i:s') . "\n";
            
//            // 批量更新教师表的user_id（因为现在老师表和用户表的主键都是原来的member_id，所以这里不需要更新教师表的user_id了）
//            if ($user_total > 0) {
//                DB::update('update teachers join users on teachers.member_id=users.openid
//                           set teachers.user_id=users.id
//                           where teachers.school_id=? and teachers.user_id is null', [$school_id]);
//
//                echo "学校ID：" . $school_id . "，批量更新教师表user_id " . date('Y-m-d H:i:s') . "\n";
//            }
        } catch (QueryException $e) {
            echo "迁移过程发生错误：" . $e->getMessage() . "\n";
        }
    }
}