# TeacherSeeder 性能优化指南

## 已实施的优化

### 1. 解决 N+1 查询问题
- **问题**：原代码在循环中对每个 member 都单独查询 teacher 表
- **解决方案**：使用 `whereIn` 预加载所有相关的教师信息，然后通过 `keyBy` 创建映射表

```php
// 优化前：N+1 查询
foreach ($members as $member) {
    $teacher_info = DB::connection($this->connect)->table('teacher')
        ->where('member_id', $member->id)
        ->where('step', 0)
        ->first();
}

// 优化后：批量查询
$memberIds = $members->pluck('id')->toArray();
$teacherInfos = DB::connection($this->connect)->table('teacher')
    ->whereIn('member_id', $memberIds)
    ->where('step', 0)
    ->get()
    ->keyBy('member_id');
```

### 2. 事务处理优化
- 添加事务包装批量插入操作
- 在出错时回滚，保证数据一致性
- 提高批量插入的原子性

### 3. 批量大小优化
- 将批量大小从 1000 调整为 500
- 减少内存占用，提高处理效率

### 4. 内存管理优化
- 添加垃圾回收 `gc_collect_cycles()`
- 及时清理不需要的变量

### 5. 监控和日志优化
- 添加执行时间统计
- 改进日志输出格式
- 添加数据条数统计

## 建议的数据库索引优化

### 1. 源数据库（mysql_prod）索引

```sql
-- member 表索引优化
ALTER TABLE member ADD INDEX idx_role_step (role_id, step);
ALTER TABLE member ADD INDEX idx_school_step (school_id, step);

-- teacher 表索引优化  
ALTER TABLE teacher ADD INDEX idx_member_step (member_id, step);
ALTER TABLE teacher ADD INDEX idx_school_step (school_id, step);

-- role 表索引优化
ALTER TABLE role ADD INDEX idx_school_type_step (school_id, type, step);
```

### 2. 目标数据库索引

```sql
-- users 表索引优化
ALTER TABLE users ADD INDEX idx_organization_id (organization_id);
ALTER TABLE users ADD INDEX idx_role_id (role_id);

-- teachers 表索引优化
ALTER TABLE teachers ADD INDEX idx_user_id (user_id);
ALTER TABLE teachers ADD INDEX idx_school_id (school_id);
ALTER TABLE teachers ADD INDEX idx_school_campus_id (school_campus_id);

-- organizations 表索引优化
ALTER TABLE organizations ADD INDEX idx_model_type_id (model_type, model_id);
```

## 进一步优化建议

### 1. 使用批量 UPSERT
如果支持 MySQL 8.0+，可以使用 `INSERT ... ON DUPLICATE KEY UPDATE`：

```php
DB::table('users')->upsert($user_data, ['id'], [
    'organization_id', 'username', 'real_name', 'phone', 'gender', 'md5_password', 'role_id', 'creator'
]);
```

### 2. 并行处理
对于多个学校的数据迁移，可以考虑并行处理：

```php
// 使用队列系统
foreach ($schoolIds as $schoolId) {
    dispatch(new TeacherMigrationJob($schoolId));
}
```

### 3. 分页处理大数据集
对于特别大的数据集，可以使用游标分页：

```php
DB::connection($this->connect)->table('member')
    ->where('role_id', $old_role_id_str)
    ->where('step', 0)
    ->orderBy('id')
    ->lazy($this->batchSize)
    ->chunk($this->batchSize, function ($members) {
        // 处理逻辑
    });
```

### 4. 内存监控
添加内存使用监控：

```php
$memoryUsage = memory_get_usage(true);
$memoryPeak = memory_get_peak_usage(true);
echo "内存使用：" . round($memoryUsage / 1024 / 1024, 2) . "MB\n";
echo "峰值内存：" . round($memoryPeak / 1024 / 1024, 2) . "MB\n";
```

### 5. 数据库连接优化
在配置文件中优化数据库连接参数：

```php
// config/database.php
'mysql_prod' => [
    'driver' => 'mysql',
    // ... 其他配置
    'options' => [
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_STRINGIFY_FETCHES => false,
    ],
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'strict' => false,
    'engine' => null,
    // 连接池配置
    'pool' => [
        'min_connections' => 1,
        'max_connections' => 10,
        'connect_timeout' => 10.0,
        'wait_timeout' => 3.0,
        'heartbeat' => -1,
        'max_idle_time' => 60.0,
    ],
],
```

## 性能测试建议

### 1. 基准测试
在优化前后分别测试：
- 执行时间
- 内存使用量
- CPU 使用率
- 数据库连接数

### 2. 监控指标
- 每秒处理记录数（TPS）
- 平均响应时间
- 错误率
- 资源使用率

### 3. 压力测试
使用不同大小的数据集测试：
- 小数据集（< 1000 条）
- 中等数据集（1000-10000 条）
- 大数据集（> 10000 条）

## 故障排除

### 1. 常见问题
- **内存不足**：减少批量大小，增加垃圾回收
- **连接超时**：增加数据库连接超时时间
- **死锁**：检查索引，优化查询顺序

### 2. 调试技巧
- 启用 SQL 查询日志
- 使用 `EXPLAIN` 分析查询计划
- 监控慢查询日志

## 总结

通过以上优化措施，TeacherSeeder 的性能应该有显著提升：
- 减少了 N+1 查询问题
- 提高了批量操作效率
- 增强了错误处理和监控
- 优化了内存使用

建议在生产环境部署前进行充分测试，确保优化效果符合预期。
