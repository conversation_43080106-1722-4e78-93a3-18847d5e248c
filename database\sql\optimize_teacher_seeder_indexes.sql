-- TeacherSeeder 性能优化索引脚本
-- 执行前请备份数据库！

-- ===== 源数据库 (mysql_prod) 索引优化 =====

-- 1. member 表索引优化
-- 检查是否存在索引，如果不存在则创建
SELECT 'Optimizing member table indexes...' as status;

-- 复合索引：role_id + step (最重要的查询条件)
CREATE INDEX IF NOT EXISTS idx_member_role_step ON ysy_member(role_id, step);

-- 单列索引：school_id (用于按学校查询)
CREATE INDEX IF NOT EXISTS idx_member_school_id ON ysy_member(school_id);

-- 复合索引：school_id + step (用于按学校和状态查询)
CREATE INDEX IF NOT EXISTS idx_member_school_step ON ysy_member(school_id, step);

-- 主键优化：确保 id 列有序
-- ALTER TABLE ysy_member AUTO_INCREMENT = 1;

-- 2. teacher 表索引优化
SELECT 'Optimizing teacher table indexes...' as status;

-- 复合索引：member_id + step (最重要的关联查询)
CREATE INDEX IF NOT EXISTS idx_teacher_member_step ON ysy_teacher(member_id, step);

-- 单列索引：school_id
CREATE INDEX IF NOT EXISTS idx_teacher_school_id ON ysy_teacher(school_id);

-- 复合索引：school_id + step
CREATE INDEX IF NOT EXISTS idx_teacher_school_step ON ysy_teacher(school_id, step);

-- 单列索引：school_district (校区查询)
CREATE INDEX IF NOT EXISTS idx_teacher_school_district ON ysy_teacher(school_district);

-- 3. role 表索引优化
SELECT 'Optimizing role table indexes...' as status;

-- 复合索引：school_id + type + step (角色查询的核心条件)
CREATE INDEX IF NOT EXISTS idx_role_school_type_step ON ysy_role(school_id, type, step);

-- 单列索引：type (按角色类型查询)
CREATE INDEX IF NOT EXISTS idx_role_type ON ysy_role(type);

-- ===== 目标数据库索引优化 =====

-- 4. users 表索引优化
SELECT 'Optimizing users table indexes...' as status;

-- 单列索引：organization_id
CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users(organization_id);

-- 单列索引：role_id (冗余字段索引)
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);

-- 复合索引：organization_id + status (常用查询组合)
CREATE INDEX IF NOT EXISTS idx_users_org_status ON users(organization_id, status);

-- 5. teachers 表索引优化
SELECT 'Optimizing teachers table indexes...' as status;

-- 单列索引：user_id (关联查询)
CREATE INDEX IF NOT EXISTS idx_teachers_user_id ON teachers(user_id);

-- 单列索引：school_id
CREATE INDEX IF NOT EXISTS idx_teachers_school_id ON teachers(school_id);

-- 单列索引：school_campus_id
CREATE INDEX IF NOT EXISTS idx_teachers_school_campus_id ON teachers(school_campus_id);

-- 复合索引：school_id + school_campus_id (常用查询组合)
CREATE INDEX IF NOT EXISTS idx_teachers_school_campus ON teachers(school_id, school_campus_id);

-- 单列索引：teacher_id (冗余字段索引)
CREATE INDEX IF NOT EXISTS idx_teachers_teacher_id ON teachers(teacher_id);

-- 6. organizations 表索引优化
SELECT 'Optimizing organizations table indexes...' as status;

-- 复合索引：model_type + model_id (最重要的查询条件)
CREATE INDEX IF NOT EXISTS idx_organizations_model ON organizations(model_type, model_id);

-- 单列索引：model_id
CREATE INDEX IF NOT EXISTS idx_organizations_model_id ON organizations(model_id);

-- ===== 查询优化建议 =====

-- 7. 分析表统计信息（提高查询优化器效率）
SELECT 'Analyzing table statistics...' as status;

-- 源数据库表分析
ANALYZE TABLE ysy_member;
ANALYZE TABLE ysy_teacher;
ANALYZE TABLE ysy_role;

-- 目标数据库表分析
ANALYZE TABLE users;
ANALYZE TABLE teachers;
ANALYZE TABLE organizations;

-- ===== 性能监控查询 =====

-- 8. 检查索引使用情况
SELECT 'Index usage statistics:' as info;

-- 查看索引基数（选择性）
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    NULLABLE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('ysy_member', 'ysy_teacher', 'ysy_role', 'users', 'teachers', 'organizations')
ORDER BY TABLE_NAME, INDEX_NAME;

-- 9. 检查表大小和行数
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    ROUND(DATA_LENGTH / 1024 / 1024, 2) AS 'Data Size (MB)',
    ROUND(INDEX_LENGTH / 1024 / 1024, 2) AS 'Index Size (MB)',
    ROUND((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) AS 'Total Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('ysy_member', 'ysy_teacher', 'ysy_role', 'users', 'teachers', 'organizations')
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- ===== 清理和维护 =====

-- 10. 优化表（重建索引，回收空间）
-- 注意：这个操作会锁表，在生产环境中请谨慎使用
-- OPTIMIZE TABLE ysy_member;
-- OPTIMIZE TABLE ysy_teacher;
-- OPTIMIZE TABLE ysy_role;
-- OPTIMIZE TABLE users;
-- OPTIMIZE TABLE teachers;
-- OPTIMIZE TABLE organizations;

SELECT 'Index optimization completed!' as status;

-- ===== 使用说明 =====
/*
执行步骤：
1. 在测试环境先执行此脚本
2. 运行 TeacherSeeder 测试性能提升
3. 确认无问题后在生产环境执行
4. 监控执行过程中的性能指标

注意事项：
1. 创建索引会消耗额外存储空间
2. 索引会略微影响写入性能，但大幅提升查询性能
3. 在数据量大的表上创建索引可能需要较长时间
4. 建议在业务低峰期执行

性能监控：
1. 执行前后对比查询执行时间
2. 监控 CPU 和内存使用情况
3. 检查慢查询日志
4. 使用 EXPLAIN 分析查询计划
*/
