<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TeacherViewClassSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 查询新的教师基础表
        $teacherIds = DB::table('teachers')->where('school_id', $this->school_id)
            ->pluck('teacher_id','id')->toArray();
        // 查询对应的查班数据
        $viewClassList = DB::connection($this->connect)->table('teacher')
            ->whereIn('id', array_values($teacherIds))
            ->select('member_id','view_class_ids')
            ->get()->toArray();
        foreach ($viewClassList as $item){
            // 查询查班数据
            $teacherId = $item->member_id;
            if(empty($item->view_class_ids)){
                continue;
            }
            $viewClassIds = explode(',',$item->view_class_ids);
            // 转化班级数据，找到新的班级id
            $listData = $this->transformClass($viewClassIds);
            foreach ($listData as $item_class){
                if(!empty($item_class['class_id'])){
                    $viewData = [
                        'teacher_id' => $teacherId,
                        'class_id' => $item_class['class_id'],
                        'school_year' => $item_class['school_year'],//教学年份
                        'created_at' => date('Y-m-d H:i:s',time()),
                        'updated_at' => date('Y-m-d H:i:s',time()),
                    ];
                    // 保存数据
                    DB::table('teacher_view_classes')->insert($viewData);
                }

            }
        }
    }
    /**
     * 转换班级数据
     */
    public function transformClass($classIds): array
    {
        // 查询班级数据
        $classList = DB::connection($this->connect)->table('class')
            ->join('grade', 'grade.id', '=', 'class.grade_id')
            ->whereIn('class.id', $classIds)
            ->where('class.school_id', $this->school_id)
            ->where('class.step', 0)
            ->where('grade.step', 0)
            ->selectRaw('ysy_class.id,ysy_class.name,ysy_class.school_district,ysy_grade.grade_sort as grade_id,ysy_grade.name as grade_name')
            ->get()->toArray();
        // 转换班级数据
        // 查找对应的新的班级ID
        $classData = [];
        foreach ($classList as $item){
            $newClassInfo = $this->changeClassInfo($item);
            /*
            if(empty($newClassInfo['class_id'])){
                // 验证并判断数据准确性
                dump($item);
            }*/
            $classData[] = $newClassInfo;
            //dump($newClassInfo);
        }
        return $classData;
    }
    // 转换旧的班级数据对应到新的班级ID
    public function changeClassInfo($classInfo):array
    {
        $name = $classInfo->name;
        $grade_id = intval($classInfo->grade_id);
        $grade_name = $classInfo->grade_name;
        //  新的班级名称
        $newClassName = $this->matchClassName($name);
        $classId = DB::table('classes')
            ->where('class_name', $newClassName)
            ->where('school_id', $this->school_id)
            ->where('school_campus_id',$classInfo->school_district)
            ->where('grade_id', $grade_id)
            ->value('id');
        // 计算学年
        $school_year = $this->matchSchoolYear($grade_id, $grade_name);
        return [
            'id' => $classInfo->id,
            'class_id' => $classId,
            'school_year' => $school_year,
        ];
    }

    public function matchClassName($original_class_name): string
    {
        // 匹配获取括号里的数字并且拼上班级
        preg_match('/\((\d+)\)/', $original_class_name, $matches);
        // 没有匹配到括号
        if(!isset($matches[1])){
            // 匹配获取数字
            preg_match('/(\d+)/', $original_class_name, $matches_second);
            $data = isset($matches_second[1])?$matches_second[1].'班':$original_class_name;
        }else{
            $data = $matches[1].'班';
        }
        return $data;
    }

    /**
     * 根据入学年份和grade_sort推算学年
     * 1-5为小学 6-9为初中 10-12为高中
     * 比如入学年份grade_year为2018，grade_sort为6，那么学年就是2018
     */
    public function matchSchoolYear($grade_sort,$grade_year){
        // 判断学生所在教育阶段并计算学年
        if ($grade_sort<=5){ // 小学阶段
            $school_year = $grade_year+$grade_sort-1;
        }elseif ($grade_sort<=9){ // 初中阶段
            $school_year = $grade_year+$grade_sort-6;
        }else{// 高中阶段
            $school_year = $grade_year+$grade_sort-10;
        }
        return $school_year;
    }
}

