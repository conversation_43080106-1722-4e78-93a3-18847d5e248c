<?php

namespace Database\Seeders;

use Illuminate\Database\QueryException;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Process;

/**
 * 并行处理版本的教师数据填充
 * 通过多进程并行处理不同角色的数据，大幅提升性能
 */
class TeacherSeederParallel extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    protected int $teacherType = 3;
    protected int $batchSize = 100; // 并行处理时使用更小的批量
    protected int $maxProcesses = 4; // 最大并行进程数

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * 并行执行教师数据填充
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        $start_time = microtime(true);

        echo "开始并行处理学校ID：{$school_id} 的教师数据迁移...\n";

        // 获取学校对应的机构ID
        $orgID = DB::table('organizations')
            ->where('model_id', $this->school_id)
            ->where('model_type', 'school')
            ->value('id');

        if (!$orgID) {
            echo "错误：未找到学校ID {$school_id} 对应的机构信息\n";
            return;
        }

        try {
            // 获取学校角色列表
            $roleList = DB::connection($this->connect)->table('role')
                ->where('school_id', $school_id)
                ->where('step', 0)
                ->whereIn('type', [2, 3])
                ->get();

            if ($roleList->isEmpty()) {
                echo "未找到学校ID {$school_id} 的角色数据\n";
                return;
            }

            echo "找到 " . $roleList->count() . " 个角色，开始并行处理...\n";

            // 并行处理角色
            $this->processRolesInParallel($roleList, $orgID, $school_id);

            $end_time = microtime(true);
            $execution_time = round($end_time - $start_time, 2);

            echo "学校ID：{$school_id} 并行数据迁移完成！执行时间：{$execution_time} 秒\n";

        } catch (QueryException $e) {
            echo "迁移过程发生错误：" . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * 并行处理角色数据
     */
    private function processRolesInParallel($roleList, $orgID, $school_id): void
    {
        $processes = [];
        $activeProcesses = 0;

        foreach ($roleList as $role) {
            // 控制并发数量
            while ($activeProcesses >= $this->maxProcesses) {
                $this->waitForProcessCompletion($processes, $activeProcesses);
                usleep(100000); // 等待100ms
            }

            // 启动新进程处理角色
            $process = $this->startRoleProcess($role, $orgID, $school_id);
            $processes[] = $process;
            $activeProcesses++;

            echo "启动进程处理角色ID：{$role->id}，类型：{$role->type}\n";
        }

        // 等待所有进程完成
        while ($activeProcesses > 0) {
            $this->waitForProcessCompletion($processes, $activeProcesses);
            usleep(100000);
        }

        echo "所有并行进程已完成\n";
    }

    /**
     * 启动处理单个角色的进程
     */
    private function startRoleProcess($role, $orgID, $school_id)
    {
        $command = sprintf(
            'php artisan tinker --execute="
                \$seeder = new \Database\Seeders\TeacherSeederParallel(%d);
                \$seeder->processSingleRole(%d, \'%s\', %d, %d, %d);
            "',
            $this->school_id,
            $role->id,
            "0,{$role->id},0",
            $role->type,
            $orgID,
            $school_id
        );

        return Process::start($command);
    }

    /**
     * 等待进程完成
     */
    private function waitForProcessCompletion(&$processes, &$activeProcesses): void
    {
        foreach ($processes as $key => $process) {
            if (!$process->running()) {
                $output = $process->output();
                if ($output) {
                    echo $output;
                }
                
                if (!$process->successful()) {
                    echo "进程执行失败：" . $process->errorOutput() . "\n";
                }

                unset($processes[$key]);
                $activeProcesses--;
            }
        }
    }

    /**
     * 处理单个角色的数据（在子进程中执行）
     */
    public function processSingleRole($roleId, $roleIdStr, $type, $orgID, $school_id): void
    {
        $user_total = 0;
        $teacher_total = 0;
        $teacher_error_total = 0;
        $num = 1;

        echo "子进程开始处理角色ID：{$roleId}\n";

        try {
            $offset = 0;
            
            do {
                // 获取成员数据
                $members = DB::connection($this->connect)->table('member')
                    ->where('role_id', $roleIdStr)
                    ->where('step', 0)
                    ->orderBy('id')
                    ->offset($offset)
                    ->limit($this->batchSize)
                    ->get();

                if ($members->isEmpty()) {
                    break;
                }

                // 预加载教师信息
                $memberIds = $members->pluck('id')->toArray();
                $teacherInfoMap = [];

                if ($type == $this->teacherType) {
                    $teacherInfos = DB::connection($this->connect)->table('teacher')
                        ->whereIn('member_id', $memberIds)
                        ->where('step', 0)
                        ->get()
                        ->keyBy('member_id');
                    $teacherInfoMap = $teacherInfos->toArray();
                }

                // 处理数据
                $result = $this->processMemberBatch($members, $roleId, $type, $orgID, $school_id, $teacherInfoMap);
                
                $user_total += $result['user_count'];
                $teacher_total += $result['teacher_count'];
                $teacher_error_total += $result['error_count'];

                echo "角色ID：{$roleId}，处理第{$num}批，用户：{$result['user_count']}，教师：{$result['teacher_count']}\n";

                $offset += $this->batchSize;
                $num++;

            } while (true);

            echo "角色ID：{$roleId} 处理完成，用户：{$user_total}，教师：{$teacher_total}，错误：{$teacher_error_total}\n";

        } catch (\Exception $e) {
            echo "角色ID：{$roleId} 处理失败：" . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * 处理成员批次数据
     */
    private function processMemberBatch($members, $roleId, $type, $orgID, $school_id, $teacherInfoMap): array
    {
        $user_data = [];
        $teacher_data = [];
        $teacher_error_data = [];

        foreach ($members as $member) {
            // 用户数据
            $user_data[] = [
                'id' => $member->id,
                'organization_id' => $orgID,
                'username' => $member->username,
                'real_name' => $member->name,
                'phone' => $member->mobile,
                'gender' => $member->gender > 1 ? 2 : 1,
                'md5_password' => $member->password,
                'role_id' => $roleId,
                'creator' => '教师',
            ];

            // 教师数据
            if ($type == $this->teacherType) {
                $teacher_info = $teacherInfoMap[$member->id] ?? null;

                if ($teacher_info) {
                    $teacher_data[] = [
                        'id' => $member->id,
                        'teacher_id' => $teacher_info->id,
                        'user_id' => $member->id,
                        'school_id' => $school_id,
                        'school_campus_id' => $teacher_info->school_district,
                        'teacher_name' => $teacher_info->name,
                        'is_psychology_teacher' => $teacher_info->is_psych,
                    ];
                } else {
                    $teacher_error_data[] = [$member->id];
                }
            }
        }

        // 批量插入
        DB::beginTransaction();
        try {
            if (!empty($user_data)) {
                DB::table('users')->insertOrIgnore($user_data);
            }

            if (!empty($teacher_data)) {
                DB::table('teachers')->insertOrIgnore($teacher_data);
            }

            DB::commit();

            return [
                'user_count' => count($user_data),
                'teacher_count' => count($teacher_data),
                'error_count' => count($teacher_error_data),
            ];

        } catch (QueryException $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
