<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * TeacherSeeder 性能测试脚本
 * 用于测试优化前后的性能差异
 */
class TeacherSeederPerformanceTest extends Seeder
{
    /**
     * 运行性能测试
     */
    public function run(): void
    {
        echo "=== TeacherSeeder 性能测试 ===\n";
        
        // 获取测试学校ID列表（取前3个学校进行测试）
        $testSchoolIds = DB::connection('mysql_prod')->table('role')
            ->where('step', 0)
            ->whereIn('type', [2, 3])
            ->distinct()
            ->limit(3)
            ->pluck('school_id')
            ->toArray();
            
        if (empty($testSchoolIds)) {
            echo "未找到测试数据\n";
            return;
        }
        
        echo "测试学校ID：" . implode(', ', $testSchoolIds) . "\n";
        echo "开始性能测试...\n\n";
        
        $totalStartTime = microtime(true);
        $results = [];
        
        foreach ($testSchoolIds as $schoolId) {
            echo "--- 测试学校ID：{$schoolId} ---\n";
            
            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);
            
            try {
                // 运行优化后的 TeacherSeeder
                $seeder = new TeacherSeeder_2($schoolId);
                $seeder->run();
                
                $endTime = microtime(true);
                $endMemory = memory_get_usage(true);
                $peakMemory = memory_get_peak_usage(true);
                
                $executionTime = round($endTime - $startTime, 2);
                $memoryUsed = round(($endMemory - $startMemory) / 1024 / 1024, 2);
                $peakMemoryMB = round($peakMemory / 1024 / 1024, 2);
                
                $results[$schoolId] = [
                    'execution_time' => $executionTime,
                    'memory_used' => $memoryUsed,
                    'peak_memory' => $peakMemoryMB,
                    'status' => 'success'
                ];
                
                echo "执行时间：{$executionTime} 秒\n";
                echo "内存使用：{$memoryUsed} MB\n";
                echo "峰值内存：{$peakMemoryMB} MB\n";
                echo "状态：成功\n\n";
                
            } catch (\Exception $e) {
                $endTime = microtime(true);
                $executionTime = round($endTime - $startTime, 2);
                
                $results[$schoolId] = [
                    'execution_time' => $executionTime,
                    'memory_used' => 0,
                    'peak_memory' => 0,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
                
                echo "执行时间：{$executionTime} 秒\n";
                echo "状态：失败\n";
                echo "错误：" . $e->getMessage() . "\n\n";
            }
            
            // 强制垃圾回收
            gc_collect_cycles();
        }
        
        $totalEndTime = microtime(true);
        $totalExecutionTime = round($totalEndTime - $totalStartTime, 2);
        
        // 输出汇总报告
        $this->printSummaryReport($results, $totalExecutionTime);
    }
    
    /**
     * 打印汇总报告
     */
    private function printSummaryReport(array $results, float $totalTime): void
    {
        echo "=== 性能测试汇总报告 ===\n";
        echo "总执行时间：{$totalTime} 秒\n";
        echo "测试学校数量：" . count($results) . "\n\n";
        
        $successCount = 0;
        $totalExecutionTime = 0;
        $totalMemoryUsed = 0;
        $maxPeakMemory = 0;
        
        echo "详细结果：\n";
        echo str_pad("学校ID", 10) . str_pad("状态", 8) . str_pad("执行时间(秒)", 12) . str_pad("内存使用(MB)", 12) . str_pad("峰值内存(MB)", 12) . "\n";
        echo str_repeat("-", 60) . "\n";
        
        foreach ($results as $schoolId => $result) {
            echo str_pad($schoolId, 10);
            echo str_pad($result['status'], 8);
            echo str_pad($result['execution_time'], 12);
            echo str_pad($result['memory_used'], 12);
            echo str_pad($result['peak_memory'], 12);
            
            if ($result['status'] === 'failed') {
                echo " (错误: " . substr($result['error'], 0, 30) . "...)";
            }
            echo "\n";
            
            if ($result['status'] === 'success') {
                $successCount++;
                $totalExecutionTime += $result['execution_time'];
                $totalMemoryUsed += $result['memory_used'];
                $maxPeakMemory = max($maxPeakMemory, $result['peak_memory']);
            }
        }
        
        echo str_repeat("-", 60) . "\n";
        
        if ($successCount > 0) {
            $avgExecutionTime = round($totalExecutionTime / $successCount, 2);
            $avgMemoryUsed = round($totalMemoryUsed / $successCount, 2);
            
            echo "成功率：" . round(($successCount / count($results)) * 100, 1) . "%\n";
            echo "平均执行时间：{$avgExecutionTime} 秒\n";
            echo "平均内存使用：{$avgMemoryUsed} MB\n";
            echo "最大峰值内存：{$maxPeakMemory} MB\n";
            
            // 性能评估
            echo "\n=== 性能评估 ===\n";
            if ($avgExecutionTime < 30) {
                echo "✅ 执行速度：优秀（< 30秒）\n";
            } elseif ($avgExecutionTime < 60) {
                echo "⚠️  执行速度：良好（30-60秒）\n";
            } else {
                echo "❌ 执行速度：需要优化（> 60秒）\n";
            }
            
            if ($maxPeakMemory < 128) {
                echo "✅ 内存使用：优秀（< 128MB）\n";
            } elseif ($maxPeakMemory < 256) {
                echo "⚠️  内存使用：良好（128-256MB）\n";
            } else {
                echo "❌ 内存使用：需要优化（> 256MB）\n";
            }
        } else {
            echo "所有测试都失败了\n";
        }
        
        echo "\n=== 优化建议 ===\n";
        if ($successCount < count($results)) {
            echo "- 检查失败的学校数据，确保数据完整性\n";
        }
        if ($maxPeakMemory > 256) {
            echo "- 考虑进一步减少批量大小\n";
            echo "- 增加更频繁的垃圾回收\n";
        }
        if ($totalExecutionTime / $successCount > 60) {
            echo "- 检查数据库索引是否优化\n";
            echo "- 考虑使用并行处理\n";
        }
        
        echo "\n测试完成！\n";
    }
}
